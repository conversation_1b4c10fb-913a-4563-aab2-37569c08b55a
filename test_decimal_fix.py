#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试decimal字段修复的脚本
验证空字符串和无效值的处理是否正确
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 从修复后的导入器中导入函数
from importer_text_fields import convert_excel_row_to_db_row, EXCEL_COLUMNS, DB_COLUMNS

def test_decimal_field_handling():
    """测试decimal字段的处理逻辑"""
    print("=== 测试decimal字段处理逻辑 ===")
    
    # 创建测试数据 - 模拟Excel行数据（92列）
    test_cases = [
        {
            "name": "正常数值",
            "货品总优惠": "10.50",
            "expected": "10.50"
        },
        {
            "name": "空字符串",
            "货品总优惠": "",
            "expected": None
        },
        {
            "name": "空白字符串",
            "货品总优惠": "   ",
            "expected": None
        },
        {
            "name": "None值",
            "货品总优惠": None,
            "expected": None
        },
        {
            "name": "零值",
            "货品总优惠": "0",
            "expected": "0"
        },
        {
            "name": "负数",
            "货品总优惠": "-5.25",
            "expected": "-5.25"
        },
        {
            "name": "无效字符串",
            "货品总优惠": "abc",
            "expected": None
        },
        {
            "name": "数字类型",
            "货品总优惠": 15.75,
            "expected": 15.75
        }
    ]
    
    # 找到'货品总优惠'在Excel列中的索引
    discount_index = EXCEL_COLUMNS.index('货品总优惠')
    print(f"'货品总优惠'字段在Excel中的索引: {discount_index}")
    
    # 找到'货品总优惠'在数据库列中的索引
    db_discount_index = DB_COLUMNS.index('货品总优惠')
    print(f"'货品总优惠'字段在数据库中的索引: {db_discount_index}")
    
    print("\n开始测试各种情况:")
    print("-" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        # 创建92列的测试数据，所有列都设为None，只设置测试字段
        excel_row = [None] * 92
        excel_row[discount_index] = test_case["货品总优惠"]
        
        # 转换为数据库行
        db_row = convert_excel_row_to_db_row(excel_row)
        
        # 获取转换后的值
        actual_value = db_row[db_discount_index]
        expected_value = test_case["expected"]
        
        # 检查结果
        if actual_value == expected_value:
            status = "✅ 通过"
        else:
            status = "❌ 失败"
        
        print(f"测试 {i}: {test_case['name']}")
        print(f"  输入值: {repr(test_case['货品总优惠'])}")
        print(f"  期望值: {repr(expected_value)}")
        print(f"  实际值: {repr(actual_value)}")
        print(f"  结果: {status}")
        print()

def test_multiple_decimal_fields():
    """测试多个decimal字段的处理"""
    print("=== 测试多个decimal字段处理 ===")
    
    # 创建包含多个decimal字段的测试数据
    excel_row = [None] * 92
    
    # 设置一些decimal字段的测试值
    decimal_test_values = {
        '货品总优惠': '',      # 空字符串
        '订单总优惠': '10.5',   # 正常数值
        '邮费': None,          # None值
        '货品成交价': '  ',     # 空白字符串
        '毛利率': 'invalid',   # 无效字符串
        '实际重量': 2.5        # 数字类型
    }
    
    # 设置测试值
    for field_name, test_value in decimal_test_values.items():
        if field_name in EXCEL_COLUMNS:
            field_index = EXCEL_COLUMNS.index(field_name)
            excel_row[field_index] = test_value
    
    # 转换为数据库行
    db_row = convert_excel_row_to_db_row(excel_row)
    
    print("多字段测试结果:")
    print("-" * 40)
    
    for field_name, input_value in decimal_test_values.items():
        if field_name in DB_COLUMNS:
            db_index = DB_COLUMNS.index(field_name)
            output_value = db_row[db_index]
            
            print(f"{field_name}:")
            print(f"  输入: {repr(input_value)}")
            print(f"  输出: {repr(output_value)}")
            print()

def test_data_integrity():
    """测试数据完整性 - 确保行长度正确"""
    print("=== 测试数据完整性 ===")
    
    # 创建测试数据
    excel_row = [f"test_value_{i}" for i in range(92)]
    
    # 转换
    db_row = convert_excel_row_to_db_row(excel_row)
    
    print(f"Excel行长度: {len(excel_row)}")
    print(f"数据库行长度: {len(db_row)}")
    print(f"期望数据库行长度: {len(DB_COLUMNS)}")
    
    if len(db_row) == len(DB_COLUMNS):
        print("✅ 行长度正确")
    else:
        print("❌ 行长度不正确")
    
    # 检查是否有缺失的列被正确填充为None
    missing_columns = set(DB_COLUMNS) - set(EXCEL_COLUMNS)
    print(f"\n缺失的列: {missing_columns}")
    
    for missing_col in missing_columns:
        db_index = DB_COLUMNS.index(missing_col)
        value = db_row[db_index]
        if value is None:
            print(f"✅ {missing_col}: 正确填充为None")
        else:
            print(f"❌ {missing_col}: 填充值不正确 - {repr(value)}")

if __name__ == "__main__":
    print("开始测试decimal字段修复...")
    print("=" * 60)
    
    try:
        test_decimal_field_handling()
        print("\n" + "=" * 60)
        test_multiple_decimal_fields()
        print("\n" + "=" * 60)
        test_data_integrity()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！")
        print("\n建议:")
        print("1. 如果所有测试都通过，可以使用修复后的importer_text_fields.py")
        print("2. 在实际导入前，建议先用小批量数据测试")
        print("3. 监控导入过程中的错误日志")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
