#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修改数据库字段类型：将decimal字段改为text类型
"""

import pymysql

# 数据库连接配置
DB_CONFIG = {
    'host': '************',
    'user': 'lxt',
    'password': 'Lxt0307+',
    'database': 'qq_day_sale',
    'charset': 'utf8mb4'
}

TARGET_TABLE = 'wdt_saledetail'

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ 数据库连接成功")
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def show_table_structure():
    """显示表结构"""
    connection = get_db_connection()
    if not connection:
        return
    
    try:
        cursor = connection.cursor()
        
        # 查询表结构
        sql = f"DESCRIBE {TARGET_TABLE}"
        cursor.execute(sql)
        
        print(f"\n=== 表 {TARGET_TABLE} 的当前结构 ===")
        print(f"{'字段名':<20} {'类型':<20} {'是否为空':<10} {'键':<10} {'默认值':<15} {'额外'}")
        print("-" * 90)
        
        for row in cursor.fetchall():
            field, type_info, null, key, default, extra = row
            default_str = str(default) if default is not None else 'NULL'
            print(f"{field:<20} {type_info:<20} {null:<10} {key:<10} {default_str:<15} {extra}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 查询表结构失败: {str(e)}")

def check_specific_fields():
    """检查特定字段的详细信息"""
    connection = get_db_connection()
    if not connection:
        return
    
    try:
        cursor = connection.cursor()
        
        # 查询特定字段信息
        fields_to_check = ['订单包装成本', '货品原单价']
        
        print(f"\n=== 检查问题字段的详细信息 ===")
        
        for field in fields_to_check:
            sql = f"""
            SELECT COLUMN_NAME, DATA_TYPE, COLUMN_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = '{DB_CONFIG['database']}' 
            AND TABLE_NAME = '{TARGET_TABLE}' 
            AND COLUMN_NAME = '{field}'
            """
            
            cursor.execute(sql)
            result = cursor.fetchone()
            
            if result:
                col_name, data_type, col_type, is_nullable, col_default, comment = result
                print(f"\n字段: {col_name}")
                print(f"  数据类型: {data_type}")
                print(f"  完整类型: {col_type}")
                print(f"  允许NULL: {is_nullable}")
                print(f"  默认值: {col_default}")
                print(f"  注释: {comment}")
            else:
                print(f"\n❌ 未找到字段: {field}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 检查字段信息失败: {str(e)}")

def modify_fields_to_text():
    """将指定字段修改为text类型"""
    connection = get_db_connection()
    if not connection:
        return
    
    try:
        cursor = connection.cursor()
        
        # 要修改的字段
        fields_to_modify = [
            ('订单包装成本', '订单包装成本'),
            ('货品原单价', '货品原单价')
        ]
        
        print(f"\n=== 开始修改字段类型 ===")
        
        for field_name, comment in fields_to_modify:
            print(f"\n正在修改字段: {field_name}")
            
            # 构建ALTER TABLE语句
            sql = f"""
            ALTER TABLE {TARGET_TABLE} 
            MODIFY COLUMN `{field_name}` TEXT COLLATE utf8mb4_unicode_ci COMMENT '{comment}'
            """
            
            print(f"执行SQL: {sql}")
            
            try:
                cursor.execute(sql)
                print(f"✅ 字段 '{field_name}' 修改成功")
            except Exception as field_error:
                print(f"❌ 字段 '{field_name}' 修改失败: {str(field_error)}")
        
        # 提交更改
        connection.commit()
        print(f"\n✅ 所有更改已提交到数据库")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 修改字段类型失败: {str(e)}")

def verify_changes():
    """验证修改结果"""
    connection = get_db_connection()
    if not connection:
        return
    
    try:
        cursor = connection.cursor()
        
        print(f"\n=== 验证修改结果 ===")
        
        fields_to_verify = ['订单包装成本', '货品原单价']
        
        for field in fields_to_verify:
            sql = f"""
            SELECT COLUMN_NAME, DATA_TYPE, COLUMN_TYPE, IS_NULLABLE, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = '{DB_CONFIG['database']}' 
            AND TABLE_NAME = '{TARGET_TABLE}' 
            AND COLUMN_NAME = '{field}'
            """
            
            cursor.execute(sql)
            result = cursor.fetchone()
            
            if result:
                col_name, data_type, col_type, is_nullable, col_default = result
                print(f"\n字段: {col_name}")
                print(f"  新数据类型: {data_type}")
                print(f"  完整类型: {col_type}")
                print(f"  允许NULL: {is_nullable}")
                
                if data_type.lower() == 'text':
                    print(f"  ✅ 修改成功！")
                else:
                    print(f"  ❌ 修改失败，当前类型仍为: {data_type}")
            else:
                print(f"\n❌ 未找到字段: {field}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 验证修改结果失败: {str(e)}")

def main():
    """主函数"""
    print("=== 数据库字段类型修改工具 ===")
    print(f"目标数据库: {DB_CONFIG['host']}/{DB_CONFIG['database']}")
    print(f"目标表: {TARGET_TABLE}")
    print(f"要修改的字段: 订单包装成本, 货品原单价")
    print(f"修改目标: decimal → text")
    
    # 1. 显示当前表结构
    print("\n" + "="*50)
    print("步骤1: 查看当前表结构")
    show_table_structure()
    
    # 2. 检查特定字段
    print("\n" + "="*50)
    print("步骤2: 检查问题字段详细信息")
    check_specific_fields()
    
    # 3. 确认是否继续
    print("\n" + "="*50)
    print("步骤3: 确认修改")
    confirm = input("是否继续修改字段类型？(y/N): ").strip().lower()
    
    if confirm in ['y', 'yes']:
        # 4. 执行修改
        print("\n" + "="*50)
        print("步骤4: 执行字段类型修改")
        modify_fields_to_text()
        
        # 5. 验证结果
        print("\n" + "="*50)
        print("步骤5: 验证修改结果")
        verify_changes()
        
        print("\n🎉 字段类型修改完成！")
        print("现在可以重新运行数据导入程序，应该不会再出现decimal类型错误。")
    else:
        print("\n❌ 用户取消操作")

if __name__ == "__main__":
    main()
