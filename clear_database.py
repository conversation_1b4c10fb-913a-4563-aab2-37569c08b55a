#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清空目标数据库表数据的脚本
"""

import pymysql
import sys

# 数据库连接信息
DB_CONFIG = {
    'host': '************',
    'user': 'lxt',
    'password': 'Lxt0307+',
    'database': 'qq_day_sale'
}

TARGET_TABLE = 'wdt_saledetail'

def get_db_connection():
    """创建数据库连接"""
    try:
        return pymysql.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def check_table_exists(cursor):
    """检查目标表是否存在"""
    try:
        cursor.execute(f"SHOW TABLES LIKE '{TARGET_TABLE}'")
        result = cursor.fetchone()
        return result is not None
    except Exception as e:
        print(f"❌ 检查表存在性时出错: {str(e)}")
        return False

def get_record_count(cursor):
    """获取表中的记录数"""
    try:
        cursor.execute(f"SELECT COUNT(*) FROM {TARGET_TABLE}")
        count = cursor.fetchone()[0]
        return count
    except Exception as e:
        print(f"❌ 获取记录数时出错: {str(e)}")
        return -1

def clear_table_data(cursor):
    """清空表数据"""
    try:
        # 使用 TRUNCATE 命令清空表数据（比 DELETE 更快，会重置自增ID）
        cursor.execute(f"TRUNCATE TABLE {TARGET_TABLE}")
        print(f"✅ 成功清空表 {TARGET_TABLE} 的所有数据")
        return True
    except Exception as e:
        print(f"❌ 清空表数据时出错: {str(e)}")
        # 如果 TRUNCATE 失败，尝试使用 DELETE
        try:
            print("🔄 尝试使用 DELETE 命令清空数据...")
            cursor.execute(f"DELETE FROM {TARGET_TABLE}")
            print(f"✅ 成功使用 DELETE 清空表 {TARGET_TABLE} 的所有数据")
            return True
        except Exception as delete_error:
            print(f"❌ DELETE 命令也失败: {str(delete_error)}")
            return False

def reset_auto_increment(cursor):
    """重置自增ID"""
    try:
        cursor.execute(f"ALTER TABLE {TARGET_TABLE} AUTO_INCREMENT = 1")
        print(f"✅ 成功重置表 {TARGET_TABLE} 的自增ID")
        return True
    except Exception as e:
        print(f"⚠️ 重置自增ID时出错（可忽略）: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🗑️  数据库表清空工具")
    print("=" * 60)
    print(f"目标数据库: {DB_CONFIG['host']}:{DB_CONFIG['database']}")
    print(f"目标表: {TARGET_TABLE}")
    print("=" * 60)
    
    # 创建数据库连接
    connection = get_db_connection()
    if not connection:
        sys.exit(1)
    
    try:
        cursor = connection.cursor()
        
        # 检查表是否存在
        print("🔍 检查目标表是否存在...")
        if not check_table_exists(cursor):
            print(f"❌ 表 {TARGET_TABLE} 不存在！")
            return
        
        print(f"✅ 表 {TARGET_TABLE} 存在")
        
        # 获取当前记录数
        print("📊 获取当前记录数...")
        record_count = get_record_count(cursor)
        if record_count == -1:
            print("❌ 无法获取记录数，操作终止")
            return
        
        print(f"📈 当前记录数: {record_count}")
        
        if record_count == 0:
            print("ℹ️  表中没有数据，无需清空")
            return
        
        # 确认操作
        print("\n⚠️  警告：此操作将删除表中的所有数据！")
        print(f"   将要删除 {record_count} 条记录")
        
        # 在脚本中自动确认（如果需要交互式确认，可以取消注释下面的代码）
        # confirm = input("\n是否确认继续？(输入 'YES' 确认): ")
        # if confirm != 'YES':
        #     print("❌ 操作已取消")
        #     return
        
        print("\n🚀 开始清空数据...")
        
        # 清空表数据
        if clear_table_data(cursor):
            # 提交事务
            connection.commit()
            
            # 重置自增ID
            reset_auto_increment(cursor)
            connection.commit()
            
            # 验证清空结果
            final_count = get_record_count(cursor)
            if final_count == 0:
                print(f"\n🎉 数据清空完成！")
                print(f"   删除了 {record_count} 条记录")
                print(f"   当前记录数: {final_count}")
            else:
                print(f"\n⚠️  清空可能不完整，当前记录数: {final_count}")
        else:
            print("\n❌ 数据清空失败")
            connection.rollback()
    
    except Exception as e:
        print(f"\n❌ 执行过程中出现错误: {str(e)}")
        connection.rollback()
    
    finally:
        cursor.close()
        connection.close()
        print("\n🔐 数据库连接已关闭")

if __name__ == "__main__":
    main()
